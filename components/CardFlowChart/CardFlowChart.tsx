import Image from 'next/image';
import React from 'react';
import { getServerSideTranslation } from '@/app/i18n';
import { Typography, Container, Button } from '@/lib/components';
import { StyledCard, StyledMiddleContainer } from './styled';
import { CRM_LOGIN_URL, CRM_SIGNUP_URL } from '@/constants.ts';

const CardFlowChart = async ({ lng }: { lng: string }) => {
  const { t } = await getServerSideTranslation(lng);

  return (
    <Container fullWidth maxWidth='1920px' className='py-[60px] md:py-24'>
      <Typography
        type='h1'
        text={t('whoIsAidsupplyFor')}
        textAlign='center'
        margin='0 0 44px 0'
      />
      <div className='flex gap-[44px]'>
        <div className='flex basis-1/2 justify-center rounded-[24px] bg-[#E6F0FF80] py-[45px] max-sm:hidden'>
          <Image
            className=''
            src='/landing/infographics_aid_en.svg'
            alt='infographics'
            width={440}
            height={431}
          />
        </div>
        <StyledMiddleContainer>
          <StyledCard gap='12px'>
            <Typography text={t('ukrainianBusiness')} type='h4' />
          </StyledCard>
          <StyledCard gap='12px'>
            {/*<Icon />*/}
            <Typography text={t('civilOrganizationsThatHelp')} type='h4' />
          </StyledCard>
          <StyledCard gap='12px'>
            <Typography text={t('internationalAndUkrainian')} type='h4' />
          </StyledCard>
          <StyledCard gap='12px'>
            <Typography text={t('warehouseAndLogistics')} type='h4' />
          </StyledCard>
          <div className='mt-5 flex gap-[10px] max-sm:flex-col'>
            <a className='w-full' href={CRM_LOGIN_URL} target='_blank'>
              <Button text={t('loginForMembers')} uppercase={false} fullWidth />
            </a>
            <a className='w-full' href={CRM_SIGNUP_URL} target='_blank'>
              <Button
                fullWidth
                variant='bordered'
                text={t('becomeMember')}
                uppercase={false}
              />
            </a>
          </div>
        </StyledMiddleContainer>
      </div>
    </Container>
  );
};
export default CardFlowChart;
