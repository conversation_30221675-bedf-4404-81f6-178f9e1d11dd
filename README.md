This is a [Next.js](https://nextjs.org/) project bootstrapped with [`create-next-app`](https://github.com/vercel/next.js/tree/canary/packages/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/basic-features/font-optimization) to automatically optimize and load Inter, a custom Google Font.

## Translations in the project

The project uses https://react.i18next.com/ for translations. The dictionaries are located in `app/i18n/locales` folder.

If you use translations in the client component, use the `useTranslation` hook from the `app/i18n/client.js` (check out the Header for the example).

If you use translations in the server component, use the **async** `useTranslation` function from the `app/i18n/index.js` (check out the Footer for the example).

### Translation dictionaries

Let's keep the keys inside the `common.js` file in alphabetical order so that it’s easy to find if we’ve already had that key translated.

Let’s name the key exactly as the English original text is, but written in camelCase.
If the original text is long, let’s name the key creatively, extracting the essence of its meaning.

## [Components library](https://github.com/aidsupply/components)

We use our custom library (**@aidsupply/components**) for some of the components. Import happens in this file: `lib/components.ts` so if you need to add a new component, add it there.
Then import the component you want to use from the `lib/components.ts` file.

The components that are required to be used from the library for the `theme.js` file to work properly are the following:
`Typography, Label, Badge, UiLink, Tag, Button, Menu, Checkbox, Input, PopupAlert, Switch, RatingView, Tabs, ExpansionPanel, Select`

If you run `npm run dev` command you will see the **Storybook** on [localhost:6660](). There is a bug with extra knobs when you click on each next component, so you need to reload the page to see the correct knobs.
You can play with `theme.js` file (in `.storybook` folder) and see how it affects the components.

## How/where to use some of the components from the library

Use the `Container` component to wrap the content of the page where needed. It will add the max-width to the page considering different breakpoints.

For the links use the `UiLink` component. It's **default** **font-size is 14px**, **font-weight is 600**; you can change them by props.
You can also change the color by the prop `themeColor` - one of the following: `'primary.main', 'secondary.main', 'general.dark', 'general.light', 'general.gray3', 'general.gray4', 'general.gray5', 'status.new', 'status.error',`.

For all the texts use the `Typography` or the `Label` component.
Typography types are the following: `h1, h2, h3, h4, caption1, caption2, body1, body2`.
Label types are the following: `bigger, smaller, uppercase`.

### Texts mapping from the design to the components library's Typography and Label types

- **heading/heading 1** - `Typography` with `h1` type
- **heading/heading 2** - `Typography` with `h2` type
- **heading/heading 3** - `Typography` with `h3` type
- **heading/heading 4** - `Typography` with `h4` type
- **body/body1** - `Typography` with `body1` type
- **body/body3** - `Typography` with `body2` type
- **typography/button 1** - `Typography` with `button` type
- **typography/button 2** - `Typography` with `caption3` type
- **typography/Input 2** - `Typography` with `caption2` type OR `Label` with `bigger` type

- **body/body 2** - `Label` with `smaller` type
- **label/label1** - `Label` with `smaller` type
- **label/label 2** - `Label` with `smaller` type and `fontWeight=600`
- **label/label5** - `Label` with `bigger` or `uppercase` type

## Styling

We can use either **styled-components** or **Tailwind** for styling.

### Styled-components

The global styles are located in the `app/GlobalStyles.ts` file.
The theme is located in the `app/theme.ts` file.

If you want to use the theme in the component, you can import it directly from `app/theme.ts` file.

**If you create a new styled component, please name it with the `Styled` prefix, e.g. `StyledLoginButton`.**

## Environmental variables

The environment variables are located in the `.env.local` file.

If you need them in the browser, it is necessary to prefix them with `NEXT_PUBLIC_`.

Variables without the `NEXT_PUBLIC_` prefix are only available in the server environment.

## API documentation

View, access, and interact with API endpoints and documentation via Swagger UI:

[https://api.aidsupply.org/docs](https://api.aidsupply.org/docs)

:)
